{% from "govuk/components/button/macro.njk" import govukButton %}
{% from "govuk/components/table/macro.njk" import govukTable %}
{% from "../../components/moj-cas-page-header-actions/macro.njk" import mojCasPageHeaderActions %}
{% from "govuk/components/back-link/macro.njk" import govukBackLink %}
{% from "govuk/components/input/macro.njk" import govukInput %}
{% from "moj/components/sub-navigation/macro.njk" import mojSubNavigation %}

{% extends "../../partials/layout.njk" %}

{% set pageTitle = statusDisplayName + " properties - " + applicationName %}
{% set mainClasses = "app-container govuk-body" %}

{% block beforeContent %}
    {{ govukBackLink({
        text: "Back",
        href: "/",
        classes: 'govuk-!-display-none-print'
    }) }}
{% endblock %}

{% block content %}
    {% include "../../_messages.njk" %}

    <h1 class="govuk-heading-l">Manage properties</h1>

    {{ mojSubNavigation({
        label: 'Property status navigation',
        items: subNavArr
    }) }}

    <div class="govuk-grid-row">
        <div class="govuk-grid-column-two-thirds">
            <h2 class="govuk-heading-m">{{ statusDisplayName }} properties</h2>
        </div>
        <div class="govuk-grid-column-one-third">
            <div class="govuk-button-group" style="text-align: right;">
                <a href="{{ paths.premises.new() }}" class="govuk-button govuk-button--secondary">Add a property</a>
            </div>
        </div>
    </div>

    <div class="govuk-template govuk-!-padding-8 govuk-!-padding-bottom-static-3 govuk-!-margin-bottom-8">
        <form action="{{ paths.premises.index() }}" method="get">
            {{ govukInput({
                label: {
                    classes: 'govuk-label--m',
                    text: searchLabel
                },
                hint: {
                    text: 'Search by road or postcode'
                },
                name: 'postcodeOrAddress',
                id: 'postcodeOrAddress',
                value: params.postcodeOrAddress
            }) }}

            {% if params.status %}
                <input type="hidden" name="status" value="{{ params.status }}">
            {% endif %}

            <div class="govuk-button-group">
                {{ govukButton({
                    text: 'Search',
                    attributes: { id: 'search-button' },
                    preventDoubleClick: true
                }) }}

                <a class="govuk-link" href="{{ paths.premises.index() }}">Clear</a>
            </div>
        </form>
    </div>

    {% if (tableRows | length > 0) %}
        <h3 class="govuk-heading-s">{{ premisesCounts.totalProperties }} {{ statusDisplayName.toLowerCase() }} properties</h3>

        {% if params.status === 'active' and premisesCounts.totalOnlineBedspaces is defined %}
            <div class="govuk-grid-row govuk-!-margin-bottom-4">
                <div class="govuk-grid-column-one-half">
                    <p class="govuk-body"><strong>Online bedspaces:</strong> {{ premisesCounts.totalOnlineBedspaces }}</p>
                </div>
                <div class="govuk-grid-column-one-half">
                    <p class="govuk-body"><strong>Upcoming bedspaces:</strong> {{ premisesCounts.totalUpcomingBedspaces }}</p>
                </div>
            </div>
        {% endif %}

        {{ govukTable({
            attributes: {
                'data-module': 'moj-sortable-table'
            },
            captionClasses: "govuk-table__caption--m",
            head: [
                {
                    text: "Address"
                },
                {
                    text: "Bedspaces"
                },
                {
                    text: "PDU",
                    attributes: { "aria-sort": "ascending" }
                },
                {
                    text: "Status",
                    attributes: { "aria-sort": "none" }
                },
                {
                    html: '<span class="govuk-visually-hidden">Actions</span>'
                }
            ],
            rows: tableRows
        }) }}
    {% else %}
        {% if params.postcodeOrAddress %}
            <h2>{{ 'There are no results for ‘' + params.postcodeOrAddress + '’ in the list of ' + statusDisplayName.toLowerCase() + ' properties.' }}</h2>
            <p>Check your spelling or try entering a different address.</p>
        {% else %}
            <h2>There are no {{ statusDisplayName.toLowerCase() }} properties.</h2>
        {% endif %}
    {% endif %}

{% endblock %}
