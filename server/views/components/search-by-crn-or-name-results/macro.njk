{%- from "moj/components/pagination/macro.njk" import mojPagination -%}
{%- from "../results-no-data-at-all/macro.njk" import resultsNoDataAtAll -%}


{# params - Type of Object - Description #}
{# resultsHtml - String - HTML string containing the pagination and results. Currently parent view is responsible for this. We will look to moving that logic into this component #}
{# crnOrName - String - Either a CRN or Name which the user would like to search for #}
{# uiStatus - String - Specific status the record would be in #}
{# type - String - Either "bookings" or "referrals".  #}


{% macro searchByCrnOrNameResults(params) %}
    {% if (params.resultsHtml| trim |length > 0) %}
        {{ params.resultsHtml | safe | trim | indent(8)}}

    {% else %}
        {{ resultsNoDataAtAll({ type: params.type }) }}
    {% endif %}
{% endmacro %}
