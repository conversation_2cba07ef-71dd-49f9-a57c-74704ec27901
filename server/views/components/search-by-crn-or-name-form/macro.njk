{% from "govuk/components/input/macro.njk" import govukInput %}
{% from "govuk/components/button/macro.njk" import govukButton %}


{# params - Type of Object - Description #}
{# uiStatus - String - Specific status the record would be in #}
{# type - String - Either "bookings" or "referrals" #}
{# basePath - String - URL used to define where form should be submitted to and the link the "Clear" should take the user too  #}
{# crnOrName - String - Either a CRN or Name which the user would like to search for#}


{% macro searchByCrnOrNameForm(params) %}
	<form action="{{ params.basePath }}" method="get">
		<div class="govuk-grid-row">
			<div class="govuk-form-group">
				<div class="govuk-grid-column-three-quarters">
					{{ govukInput(
						{
							label: {
							classes: 'govuk-label moj-search__label govuk-!-font-weight-bold',
							text: 'Search ' + params.uiStatus + ' ' +  params.type + ' with the person’s name or CRN'
						},
							hint: {
							text: 'For example, <PERSON> or XD7364CD'
						},
							name: 'crnOrName',
							id: 'crnOrName',
							value: params.crnOrName
						}
					) }}
				</div>
				<div class="govuk-grid-column-one-quarter">
					<div class="govuk-button-group govuk-!-margin-top-6">
						{{ govukButton({ text: "Search", attributes: { id: 'search-button' }, preventDoubleClick: true}) }}
					</div>
				</div>
				<div class="govuk-grid-column-full">
					<a class="govuk-link" href="{{ params.basePath }}">Clear Search</a>
				</div>
			</div>
		</div>
	</form>
{% endmacro %}








