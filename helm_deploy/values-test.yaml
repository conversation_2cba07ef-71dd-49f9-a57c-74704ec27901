---
# Per environment values which override defaults in hmpps-temporary-accommodation-ui/values.yaml

generic-service:
  replicaCount: 2

  ingress:
    hosts:
      - temporary-accommodation-test.hmpps.service.justice.gov.uk
      - transitional-accommodation-test.hmpps.service.justice.gov.uk
    contextColour: green
    tlsSecretName: hmpps-temporary-accommodation-test-cert

  env:
    ENVIRONMENT: test
    APPROVED_PREMISES_API_URL: 'https://approved-premises-api-test.hmpps.service.justice.gov.uk'
    FIRST_INGRESS_URL: 'https://temporary-accommodation-test.hmpps.service.justice.gov.uk'
    SECOND_INGRESS_URL: 'https://transitional-accommodation-test.hmpps.service.justice.gov.uk'
    HMPPS_AUTH_URL: 'https://sign-in-dev.hmpps.service.justice.gov.uk/auth'
    TOKEN_VERIFICATION_API_URL: 'https://token-verification-api-dev.prison.service.justice.gov.uk'
    COMMUNITY_ACCOMMODATION_API_TIMEOUT_RESPONSE: 30000
    COMMUNITY_ACCOMMODATION_API_TIMEOUT_DEADLINE: 30000
    PAGINATION_ASSESSMENTS_DEFAULT_PAGE_SIZE: 100
    DOMAIN_EVENTS_EMIT_ENABLED: 'personArrived,personDeparted'

  allowlist: null

generic-prometheus-alerts:
  alertSeverity: hmpps-approved-premises
