---
# Per environment values which override defaults in hmpps-temporary-accommodation-ui/values.yaml

generic-service:
  ingress:
    hosts:
      - temporary-accommodation.hmpps.service.justice.gov.uk
      - transitional-accommodation.hmpps.service.justice.gov.uk
    contextColour: green
    tlsSecretName: hmpps-temporary-accommodation-prod-cert

  env:
    ENVIRONMENT: prod
    APPROVED_PREMISES_API_URL: 'https://approved-premises-api.hmpps.service.justice.gov.uk'
    FIRST_INGRESS_URL: 'https://temporary-accommodation.hmpps.service.justice.gov.uk'
    SECOND_INGRESS_URL: 'https://transitional-accommodation.hmpps.service.justice.gov.uk'
    HMPPS_AUTH_URL: 'https://sign-in.hmpps.service.justice.gov.uk/auth'
    TOKEN_VERIFICATION_API_URL: 'https://token-verification-api.prison.service.justice.gov.uk'
    COMMUNITY_ACCOMMODATION_API_TIMEOUT_RESPONSE: 30000
    COMMUNITY_ACCOMMODATION_API_TIMEOUT_DEADLINE: 30000
    PAGINATION_ASSESSMENTS_DEFAULT_PAGE_SIZE: 100
    DOMAIN_EVENTS_EMIT_ENABLED: 'personArrived,personDeparted'

  namespace_secrets:
    sqs-hmpps-audit-secret:
      AUDIT_SQS_QUEUE_URL: "sqs_queue_url"
      AUDIT_SQS_QUEUE_NAME: "sqs_queue_name"
